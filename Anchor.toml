[features]
seeds = false
skip-lint = false

[programs.localnet]
lb_clmm = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"
raydium_amm = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"

[programs.devnet]
lb_clmm = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"
raydium_amm = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"

[programs.mainnet]
lb_clmm = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9H24wFSUt1Mp8"
raydium_amm = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "Localnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"

[workspace]
members = [
    "programs/lb_clmm",
    "programs/raydium_amm"
]
