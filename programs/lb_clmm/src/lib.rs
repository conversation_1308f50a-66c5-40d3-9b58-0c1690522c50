use anchor_lang::prelude::*;

declare_id!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo");

#[program]
pub mod lb_clmm {
    use super::*;

    pub fn initialize(_ctx: Context<Initialize>) -> Result<()> {
        Ok(())
    }
}

#[derive(Accounts)]
pub struct Initialize {}

// Re-export commonly used types for external crates
pub use anchor_lang::prelude::*;

// Meteora CLMM related structures and functions
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct LbPair {
    pub parameters: StaticParameters,
    pub v_parameters: VParameters,
    pub bin_step: u16,
    pub pair_type: u8,
    pub active_id: i32,
    pub bin_step_seed: [u8; 2],
    pub token_x_mint: Pubkey,
    pub token_y_mint: Pubkey,
    pub token_x_reserve: Pubkey,
    pub token_y_reserve: Pubkey,
    pub protocol_fee: ProtocolFee,
    pub fee_owner: Pubkey,
    pub reward_infos: [RewardInfo; 2],
    pub oracle: Pubkey,
    pub bin_array_bitmap: [u64; 16],
    pub last_updated_at: i64,
    pub whitelisted_wallet: [Pubkey; 2],
    pub base_key: Pubkey,
    pub activation_slot: u64,
    pub swap_cap_deactivate_slot: u64,
    pub max_swapped_amount: u64,
    pub lock_durations_in_slot: u64,
    pub creator: Pubkey,
    pub reserved: [u8; 24],
}

#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct StaticParameters {
    pub base_factor: u16,
    pub filter_period: u16,
    pub decay_period: u16,
    pub reduction_factor: u16,
    pub variable_fee_control: u32,
    pub max_volatility_accumulator: u32,
    pub min_bin_id: i32,
    pub max_bin_id: i32,
    pub protocol_share: u16,
    pub padding: [u8; 6],
}

#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct VParameters {
    pub volatility_accumulator: u32,
    pub volatility_reference: u32,
    pub index_reference: i32,
    pub padding: [u8; 4],
    pub last_update_timestamp: i64,
    pub padding1: [u8; 8],
}

#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct ProtocolFee {
    pub amount_x: u64,
    pub amount_y: u64,
}

#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct RewardInfo {
    pub mint: Pubkey,
    pub vault: Pubkey,
    pub funder: Pubkey,
    pub reward_duration: u64,
    pub reward_duration_end: u64,
    pub reward_rate: u128,
    pub last_update_time: u64,
    pub cumulative_seconds_with_empty_liquidity_reward: u64,
}

// Helper functions for external use
pub fn get_bin_array_pubkey(lb_pair: &Pubkey, bin_array_index: i64) -> (Pubkey, u8) {
    Pubkey::find_program_address(
        &[
            b"bin_array",
            lb_pair.as_ref(),
            &bin_array_index.to_le_bytes(),
        ],
        &crate::ID,
    )
}

pub fn get_oracle_pubkey(lb_pair: &Pubkey) -> (Pubkey, u8) {
    Pubkey::find_program_address(
        &[b"oracle", lb_pair.as_ref()],
        &crate::ID,
    )
}

// Swap instruction data structure
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct SwapInstructionData {
    pub amount_in: u64,
    pub minimum_amount_out: u64,
}

// Error codes
#[error_code]
pub enum LbClmmError {
    #[msg("Invalid bin array")]
    InvalidBinArray,
    #[msg("Insufficient liquidity")]
    InsufficientLiquidity,
    #[msg("Slippage tolerance exceeded")]
    SlippageExceeded,
}
