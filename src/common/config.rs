use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub rpc_url: String,
    pub ws_url: String,
    pub payer_keypair_path: String,
    pub priority_fee: u64,
    pub compute_unit_limit: u32,
    pub max_slippage: f64,
    pub min_profit_threshold: u64,
    pub database_url: Option<String>,
    pub telegram_bot_token: Option<String>,
    pub telegram_chat_id: Option<String>,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            rpc_url: "https://api.mainnet-beta.solana.com".to_string(),
            ws_url: "wss://api.mainnet-beta.solana.com".to_string(),
            payer_keypair_path: "~/.config/solana/id.json".to_string(),
            priority_fee: 1000,
            compute_unit_limit: 200000,
            max_slippage: 0.01, // 1%
            min_profit_threshold: 1000, // 0.001 SOL
            database_url: None,
            telegram_bot_token: None,
            telegram_chat_id: None,
        }
    }
}

impl Config {
    pub fn from_env() -> Self {
        Self {
            rpc_url: env::var("RPC_URL").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string()),
            ws_url: env::var("WS_URL").unwrap_or_else(|_| "wss://api.mainnet-beta.solana.com".to_string()),
            payer_keypair_path: env::var("PAYER_KEYPAIR_PATH").unwrap_or_else(|_| "~/.config/solana/id.json".to_string()),
            priority_fee: env::var("PRIORITY_FEE")
                .unwrap_or_else(|_| "1000".to_string())
                .parse()
                .unwrap_or(1000),
            compute_unit_limit: env::var("COMPUTE_UNIT_LIMIT")
                .unwrap_or_else(|_| "200000".to_string())
                .parse()
                .unwrap_or(200000),
            max_slippage: env::var("MAX_SLIPPAGE")
                .unwrap_or_else(|_| "0.01".to_string())
                .parse()
                .unwrap_or(0.01),
            min_profit_threshold: env::var("MIN_PROFIT_THRESHOLD")
                .unwrap_or_else(|_| "1000".to_string())
                .parse()
                .unwrap_or(1000),
            database_url: env::var("DATABASE_URL").ok(),
            telegram_bot_token: env::var("TELEGRAM_BOT_TOKEN").ok(),
            telegram_chat_id: env::var("TELEGRAM_CHAT_ID").ok(),
        }
    }

    pub fn load_from_file(path: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let content = std::fs::read_to_string(path)?;
        let config: Config = serde_json::from_str(&content)?;
        Ok(config)
    }

    pub fn save_to_file(&self, path: &str) -> Result<(), Box<dyn std::error::Error>> {
        let content = serde_json::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }
}

// Environment configuration
#[derive(Debug, Clone)]
pub struct Environment {
    pub rpc_url: String,
    pub ws_url: String,
    pub payer_keypair_path: String,
    pub priority_fee: u64,
    pub compute_unit_limit: u32,
}

impl Environment {
    pub fn new() -> Self {
        let config = Config::from_env();
        Self {
            rpc_url: config.rpc_url,
            ws_url: config.ws_url,
            payer_keypair_path: config.payer_keypair_path,
            priority_fee: config.priority_fee,
            compute_unit_limit: config.compute_unit_limit,
        }
    }
}

impl Default for Environment {
    fn default() -> Self {
        Self::new()
    }
}
